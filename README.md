# Algorithmic Trading API

A comprehensive end-to-end algorithmic trading application built with Flask, featuring real-time market data integration, portfolio management, trading strategies, and order execution.

## 🚀 Features

- **Real-time Market Data**: Integration with Yahoo Finance, Alpha Vantage, and other data providers
- **Portfolio Management**: Track multiple portfolios, positions, and performance metrics
- **Trading Strategies**: Modular strategy framework with built-in indicators (Moving Average, RSI)
- **Order Management**: Support for market, limit, stop, and stop-limit orders
- **User Authentication**: JWT-based authentication with secure user management
- **RESTful API**: Comprehensive REST API with proper error handling and validation
- **Database Integration**: SQLAlchemy ORM with PostgreSQL/SQLite support
- **Security**: Rate limiting, input validation, and security headers
- **Testing**: Comprehensive test suite with pytest
- **Backtesting**: Strategy backtesting capabilities

## 📋 Requirements

- Python 3.8+
- PostgreSQL (optional, SQLite for development)
- Redis (for caching and background tasks)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd algo_trader
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

6. **Run the application**
   ```bash
   python run.py
   ```

## 🔧 Configuration

### Environment Variables

- `FLASK_ENV`: Application environment (development/production)
- `SECRET_KEY`: Flask secret key for sessions
- `DATABASE_URL`: Database connection string
- `JWT_SECRET_KEY`: JWT token secret key
- `ALPHA_VANTAGE_API_KEY`: Alpha Vantage API key (optional)
- `REDIS_URL`: Redis connection URL

### API Keys

To get real-time market data, you'll need API keys from:
- [Alpha Vantage](https://www.alphavantage.co/support/#api-key) (free tier available)
- [Polygon.io](https://polygon.io/) (optional)
- [IEX Cloud](https://iexcloud.io/) (optional)

## 📚 API Documentation

### Authentication

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "your_username",
  "email": "<EMAIL>",
  "password": "your_password"
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### Portfolio Management

#### Get Portfolios
```http
GET /api/v1/portfolio
Authorization: Bearer <jwt_token>
```

#### Create Portfolio
```http
POST /api/v1/portfolio
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "My Portfolio",
  "initial_value": 100000
}
```

#### Get Positions
```http
GET /api/v1/portfolio/positions?portfolio_id=1
Authorization: Bearer <jwt_token>
```

### Market Data

#### Get Real-time Quote
```http
GET /api/v1/market-data/AAPL
Authorization: Bearer <jwt_token>
```

#### Get Historical Data
```http
GET /api/v1/market-data/AAPL/history?period=1y&interval=1d
Authorization: Bearer <jwt_token>
```

#### Get Multiple Quotes
```http
POST /api/v1/market-data/quotes
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "symbols": ["AAPL", "GOOGL", "MSFT"]
}
```

### Trading

#### Create Order
```http
POST /api/v1/orders
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "portfolio_id": 1,
  "symbol": "AAPL",
  "side": "buy",
  "order_type": "market",
  "quantity": 10
}
```

#### Get Orders
```http
GET /api/v1/orders
Authorization: Bearer <jwt_token>
```

#### Cancel Order
```http
DELETE /api/v1/orders/123
Authorization: Bearer <jwt_token>
```

### Strategies

#### Create Strategy
```http
POST /api/v1/strategies
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "My MA Strategy",
  "strategy_type": "moving_average",
  "symbols": ["AAPL", "GOOGL"],
  "parameters": {
    "short_window": 20,
    "long_window": 50
  }
}
```

#### Run Backtest
```http
POST /api/v1/strategies/1/backtest
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "period": "1y"
}
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_auth.py

# Run with verbose output
pytest -v
```

## 🏗️ Architecture

```
app/
├── api/                 # REST API endpoints
├── models/             # Database models
├── services/           # Business logic services
├── utils/              # Utility functions and decorators
└── __init__.py         # Application factory

tests/                  # Test suite
config.py              # Configuration settings
run.py                 # Application entry point
```

### Key Components

- **Models**: SQLAlchemy models for users, portfolios, orders, trades, and market data
- **Services**: Business logic for portfolio management, order execution, and strategy execution
- **API**: RESTful endpoints with proper validation and error handling
- **Security**: JWT authentication, rate limiting, and input validation

## 🔒 Security Features

- JWT-based authentication
- Password hashing with werkzeug
- Rate limiting per IP and user
- Input validation and sanitization
- Security headers (CORS, XSS protection, etc.)
- SQL injection prevention through ORM

## 📈 Trading Strategies

### Built-in Strategies

1. **Moving Average Crossover**
   - Buy when short MA crosses above long MA
   - Sell when short MA crosses below long MA

2. **RSI Strategy**
   - Buy when RSI crosses above oversold level (default: 30)
   - Sell when RSI crosses below overbought level (default: 70)

### Custom Strategies

Extend the `BaseStrategy` class to create custom trading strategies:

```python
from app.services.strategy_service import BaseStrategy

class MyCustomStrategy(BaseStrategy):
    def generate_signals(self, symbol, data):
        # Implement your strategy logic
        signals = []
        # ... strategy logic ...
        return signals
    
    def get_required_parameters(self):
        return [
            {'name': 'param1', 'type': 'int', 'default': 10}
        ]
```

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "run.py"]
```

### Production Considerations

- Use PostgreSQL for production database
- Set up Redis for caching and background tasks
- Configure proper logging
- Use environment variables for sensitive configuration
- Set up SSL/TLS certificates
- Configure reverse proxy (nginx)
- Set up monitoring and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Do not use it for actual trading without proper testing and risk management. The authors are not responsible for any financial losses incurred through the use of this software.

## 📞 Support

For questions and support, please open an issue on GitHub or contact the maintainers.
