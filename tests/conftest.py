import pytest
import tempfile
import os
from app import create_app, db
from app.models.user import User
from app.models.portfolio import Portfolio
from config import TestingConfig

@pytest.fixture
def app():
    """Create application for testing."""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()
    
    # Override config for testing
    test_config = TestingConfig()
    test_config.SQLALCHEMY_DATABASE_URI = f'sqlite:///{db_path}'
    
    app = create_app('testing')
    app.config.from_object(test_config)
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()
    
    os.close(db_fd)
    os.unlink(db_path)

@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create test CLI runner."""
    return app.test_cli_runner()

@pytest.fixture
def test_user(app):
    """Create test user."""
    with app.app_context():
        user = User(username='testuser', email='<EMAIL>')
        user.set_password('testpassword')
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def test_portfolio(app, test_user):
    """Create test portfolio."""
    with app.app_context():
        portfolio = Portfolio(
            user_id=test_user.id,
            name='Test Portfolio',
            initial_value=100000,
            current_value=100000,
            cash_balance=100000
        )
        db.session.add(portfolio)
        db.session.commit()
        return portfolio

@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user."""
    response = client.post('/api/v1/auth/login', json={
        'username': 'testuser',
        'password': 'testpassword'
    })
    
    data = response.get_json()
    token = data['access_token']
    
    return {'Authorization': f'Bearer {token}'}

@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    return {
        'AAPL': {
            'symbol': 'AAPL',
            'price': 150.00,
            'bid': 149.95,
            'ask': 150.05,
            'volume': 1000000,
            'change': 2.50,
            'change_percent': 1.69
        },
        'GOOGL': {
            'symbol': 'GOOGL',
            'price': 2500.00,
            'bid': 2499.50,
            'ask': 2500.50,
            'volume': 500000,
            'change': -10.00,
            'change_percent': -0.40
        }
    }
