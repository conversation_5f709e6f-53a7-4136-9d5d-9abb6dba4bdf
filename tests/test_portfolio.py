import pytest
from app.services.portfolio_service import PortfolioService

class TestPortfolio:
    """Test portfolio endpoints."""
    
    def test_get_portfolios(self, client, auth_headers, test_portfolio):
        """Test getting user portfolios."""
        response = client.get('/api/v1/portfolio', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'portfolios' in data
        assert len(data['portfolios']) == 1
        assert data['portfolios'][0]['name'] == 'Test Portfolio'
    
    def test_create_portfolio(self, client, auth_headers):
        """Test creating a new portfolio."""
        response = client.post('/api/v1/portfolio', 
                             headers=auth_headers,
                             json={
                                 'name': 'New Portfolio',
                                 'initial_value': 50000
                             })
        
        assert response.status_code == 201
        data = response.get_json()
        assert data['portfolio']['name'] == 'New Portfolio'
        assert data['portfolio']['initial_value'] == 50000
    
    def test_create_portfolio_invalid_value(self, client, auth_headers):
        """Test creating portfolio with invalid initial value."""
        response = client.post('/api/v1/portfolio',
                             headers=auth_headers,
                             json={
                                 'name': 'Invalid Portfolio',
                                 'initial_value': -1000
                             })
        
        assert response.status_code == 400
    
    def test_get_positions(self, client, auth_headers, test_portfolio):
        """Test getting portfolio positions."""
        response = client.get(f'/api/v1/portfolio/positions?portfolio_id={test_portfolio.id}',
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'positions' in data
        assert 'summary' in data
        assert data['portfolio_id'] == test_portfolio.id
    
    def test_get_positions_no_portfolio_id(self, client, auth_headers):
        """Test getting positions without portfolio_id."""
        response = client.get('/api/v1/portfolio/positions', headers=auth_headers)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'portfolio_id parameter is required' in data['error']
    
    def test_get_positions_invalid_portfolio(self, client, auth_headers):
        """Test getting positions for invalid portfolio."""
        response = client.get('/api/v1/portfolio/positions?portfolio_id=999',
                            headers=auth_headers)
        
        assert response.status_code == 404
        data = response.get_json()
        assert 'Portfolio not found' in data['error']

class TestPortfolioService:
    """Test portfolio service methods."""
    
    def test_create_portfolio(self, app, test_user):
        """Test portfolio creation service."""
        with app.app_context():
            service = PortfolioService()
            portfolio = service.create_portfolio(
                test_user.id, 
                'Service Test Portfolio', 
                75000
            )
            
            assert portfolio is not None
            assert portfolio.name == 'Service Test Portfolio'
            assert float(portfolio.initial_value) == 75000
            assert float(portfolio.cash_balance) == 75000
    
    def test_get_user_portfolios(self, app, test_user, test_portfolio):
        """Test getting user portfolios."""
        with app.app_context():
            service = PortfolioService()
            portfolios = service.get_user_portfolios(test_user.id)
            
            assert len(portfolios) == 1
            assert portfolios[0].id == test_portfolio.id
    
    def test_calculate_portfolio_metrics(self, app, test_portfolio):
        """Test portfolio metrics calculation."""
        with app.app_context():
            service = PortfolioService()
            metrics = service.calculate_portfolio_metrics(test_portfolio.id)
            
            assert 'total_value' in metrics
            assert 'cash_balance' in metrics
            assert 'positions' in metrics
            assert metrics['portfolio_id'] == test_portfolio.id
