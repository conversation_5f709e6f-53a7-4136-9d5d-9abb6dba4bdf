import pytest
from app.models.user import User
from app import db

class TestAuth:
    """Test authentication endpoints."""
    
    def test_register_user(self, client):
        """Test user registration."""
        response = client.post('/api/v1/auth/register', json={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123'
        })
        
        assert response.status_code == 201
        data = response.get_json()
        assert 'access_token' in data
        assert data['user']['username'] == 'newuser'
        assert data['user']['email'] == '<EMAIL>'
    
    def test_register_duplicate_username(self, client, test_user):
        """Test registration with duplicate username."""
        response = client.post('/api/v1/auth/register', json={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123'
        })
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Username already exists' in data['error']
    
    def test_register_duplicate_email(self, client, test_user):
        """Test registration with duplicate email."""
        response = client.post('/api/v1/auth/register', json={
            'username': 'differentuser',
            'email': '<EMAIL>',
            'password': 'password123'
        })
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Email already exists' in data['error']
    
    def test_register_invalid_password(self, client):
        """Test registration with invalid password."""
        response = client.post('/api/v1/auth/register', json={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': '123'  # Too short
        })
        
        assert response.status_code == 400
    
    def test_login_success(self, client, test_user):
        """Test successful login."""
        response = client.post('/api/v1/auth/login', json={
            'username': 'testuser',
            'password': 'testpassword'
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'access_token' in data
        assert data['user']['username'] == 'testuser'
    
    def test_login_invalid_credentials(self, client, test_user):
        """Test login with invalid credentials."""
        response = client.post('/api/v1/auth/login', json={
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        assert response.status_code == 401
        data = response.get_json()
        assert 'Invalid credentials' in data['error']
    
    def test_login_nonexistent_user(self, client):
        """Test login with nonexistent user."""
        response = client.post('/api/v1/auth/login', json={
            'username': 'nonexistent',
            'password': 'password'
        })
        
        assert response.status_code == 401
    
    def test_get_profile(self, client, auth_headers):
        """Test getting user profile."""
        response = client.get('/api/v1/auth/register', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['user']['username'] == 'testuser'
    
    def test_get_profile_no_auth(self, client):
        """Test getting profile without authentication."""
        response = client.get('/api/v1/auth/register')
        
        assert response.status_code == 401
