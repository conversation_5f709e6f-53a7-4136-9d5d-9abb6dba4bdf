#!/usr/bin/env python3
"""
Algorithmic Trading Application
Main entry point for the Flask application.
"""

import os
import logging
from app import create_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/algo_trader.log'),
        logging.StreamHandler()
    ]
)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Create Flask application
app = create_app()

if __name__ == '__main__':
    # Get configuration from environment
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                   Algorithmic Trading API                    ║
    ║                                                              ║
    ║  🚀 Server starting on http://{host}:{port}                    ║
    ║  📊 Real-time market data integration                        ║
    ║  💼 Portfolio management system                              ║
    ║  🤖 Trading strategy engine                                  ║
    ║  📈 Order management and execution                           ║
    ║                                                              ║
    ║  API Documentation: http://{host}:{port}/api/v1              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )
