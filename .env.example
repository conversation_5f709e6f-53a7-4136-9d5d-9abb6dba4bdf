# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/algo_trader
SQLALCHEMY_DATABASE_URI=postgresql://username:password@localhost:5432/algo_trader

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_EXPIRES=3600

# API Keys
ALPHA_VANTAGE_API_KEY=D883VLCLSW8JXUMI
POLYGON_API_KEY=your-polygon-api-key
IEX_CLOUD_API_KEY=your-iex-cloud-api-key

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Trading Configuration
DEFAULT_PORTFOLIO_VALUE=100000
MAX_POSITION_SIZE=0.1
RISK_FREE_RATE=0.02

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/algo_trader.log
