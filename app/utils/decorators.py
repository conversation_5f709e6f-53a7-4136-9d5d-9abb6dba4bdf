from functools import wraps
from flask import jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import ValidationError
import logging

logger = logging.getLogger(__name__)

def handle_errors(f):
    """Decorator to handle common API errors."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error: {e.messages}")
            return jsonify({
                'error': 'Validation error',
                'messages': e.messages
            }), 400
        except ValueError as e:
            logger.warning(f"Value error: {str(e)}")
            return jsonify({
                'error': 'Invalid value',
                'message': str(e)
            }), 400
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return jsonify({
                'error': 'Internal server error',
                'message': 'An unexpected error occurred'
            }), 500
    return decorated_function

def validate_json(schema_class):
    """Decorator to validate JSON input using marshmallow schema."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Content-Type must be application/json'}), 400
            
            schema = schema_class()
            try:
                validated_data = schema.load(request.get_json())
                return f(validated_data, *args, **kwargs)
            except ValidationError as e:
                return jsonify({
                    'error': 'Validation error',
                    'messages': e.messages
                }), 400
        return decorated_function
    return decorator

def auth_required(f):
    """Decorator that combines JWT requirement with error handling."""
    @wraps(f)
    @jwt_required()
    @handle_errors
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        return f(current_user_id, *args, **kwargs)
    return decorated_function
