import hashlib
import secrets
import time
from functools import wraps
from flask import request, jsonify, current_app
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """Simple in-memory rate limiter."""
    
    def __init__(self):
        self.requests = {}
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """Check if request is allowed based on rate limit."""
        current_time = time.time()
        
        # Cleanup old entries periodically
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries(current_time)
        
        # Initialize key if not exists
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if current_time - req_time < window
        ]
        
        # Check if limit exceeded
        if len(self.requests[key]) >= limit:
            return False
        
        # Add current request
        self.requests[key].append(current_time)
        return True
    
    def _cleanup_old_entries(self, current_time: float):
        """Remove old entries to prevent memory leaks."""
        for key in list(self.requests.keys()):
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if current_time - req_time < 3600  # Keep last hour
            ]
            
            # Remove empty entries
            if not self.requests[key]:
                del self.requests[key]
        
        self.last_cleanup = current_time

# Global rate limiter instance
rate_limiter = RateLimiter()

def rate_limit(limit: int = 100, window: int = 3600, per: str = 'ip'):
    """Rate limiting decorator."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Determine rate limit key
            if per == 'ip':
                key = f"ip:{request.remote_addr}"
            elif per == 'user':
                try:
                    verify_jwt_in_request()
                    user_id = get_jwt_identity()
                    key = f"user:{user_id}"
                except:
                    key = f"ip:{request.remote_addr}"
            else:
                key = f"global:{per}"
            
            # Check rate limit
            if not rate_limiter.is_allowed(key, limit, window):
                logger.warning(f"Rate limit exceeded for {key}")
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Maximum {limit} requests per {window} seconds'
                }), 429
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def generate_api_key() -> str:
    """Generate a secure API key."""
    return secrets.token_urlsafe(32)

def hash_api_key(api_key: str) -> str:
    """Hash an API key for secure storage."""
    salt = current_app.config['SECRET_KEY'].encode()
    return hashlib.pbkdf2_hex(api_key.encode(), salt, 100000)

def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """Verify an API key against its hash."""
    return hash_api_key(api_key) == hashed_key

def sanitize_input(data: str) -> str:
    """Basic input sanitization."""
    if not isinstance(data, str):
        return data
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        data = data.replace(char, '')
    
    return data.strip()

def validate_symbol(symbol: str) -> bool:
    """Validate stock symbol format."""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic symbol validation (alphanumeric, dots, hyphens)
    import re
    pattern = r'^[A-Z0-9.-]{1,10}$'
    return bool(re.match(pattern, symbol.upper()))

def secure_headers(f):
    """Add security headers to response."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Add security headers if response is a Flask response object
        if hasattr(response, 'headers'):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            response.headers['Content-Security-Policy'] = "default-src 'self'"
        
        return response
    return decorated_function

class SecurityMiddleware:
    """Security middleware for additional protection."""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security middleware."""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """Process request before handling."""
        # Block requests with suspicious user agents
        user_agent = request.headers.get('User-Agent', '').lower()
        suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan']
        
        if any(agent in user_agent for agent in suspicious_agents):
            logger.warning(f"Blocked suspicious user agent: {user_agent}")
            return jsonify({'error': 'Forbidden'}), 403
        
        # Validate content length
        if request.content_length and request.content_length > 10 * 1024 * 1024:  # 10MB
            logger.warning(f"Request too large: {request.content_length} bytes")
            return jsonify({'error': 'Request too large'}), 413
    
    def after_request(self, response):
        """Process response after handling."""
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Remove server header
        response.headers.pop('Server', None)
        
        return response
