from flask import request, jsonify
from flask_restful import Resource
from marshmallow import Schema, fields, ValidationError
from app.models.strategy import Strategy, StrategyParameter, StrategyStatus
from app.services.strategy_service import StrategyFactory
from app.services.market_data_service import MarketDataService
from app.utils.decorators import auth_required, validate_json
from app import db
import logging

logger = logging.getLogger(__name__)

class CreateStrategySchema(Schema):
    name = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    strategy_type = fields.Str(required=True)
    symbols = fields.List(fields.Str(), required=True)
    parameters = fields.Dict(required=True)

class StrategyResource(Resource):
    """Strategy management endpoints."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    @auth_required
    def get(self, current_user_id, strategy_id=None):
        """Get strategies for the current user."""
        if strategy_id:
            # Get specific strategy
            strategy = Strategy.query.filter_by(
                id=strategy_id, 
                user_id=current_user_id, 
                is_active=True
            ).first()
            
            if not strategy:
                return {'error': 'Strategy not found'}, 404
            
            return {'strategy': strategy.to_dict()}, 200
        else:
            # Get all strategies
            strategies = Strategy.query.filter_by(
                user_id=current_user_id, 
                is_active=True
            ).all()
            
            return {
                'strategies': [strategy.to_dict() for strategy in strategies]
            }, 200
    
    @auth_required
    @validate_json(CreateStrategySchema)
    def post(self, validated_data, current_user_id):
        """Create a new strategy."""
        try:
            # Validate strategy type
            available_strategies = StrategyFactory.get_available_strategies()
            if validated_data['strategy_type'] not in available_strategies:
                return {
                    'error': f'Invalid strategy type. Available: {available_strategies}'
                }, 400
            
            # Create strategy
            strategy = Strategy(
                user_id=current_user_id,
                name=validated_data['name'],
                description=validated_data.get('description'),
                strategy_type=validated_data['strategy_type'],
                symbols=validated_data['symbols']
            )
            
            db.session.add(strategy)
            db.session.flush()  # Get strategy ID
            
            # Add parameters
            for param_name, param_value in validated_data['parameters'].items():
                # Determine parameter type
                if isinstance(param_value, bool):
                    param_type = 'bool'
                elif isinstance(param_value, int):
                    param_type = 'int'
                elif isinstance(param_value, float):
                    param_type = 'float'
                else:
                    param_type = 'string'
                
                parameter = StrategyParameter(
                    strategy_id=strategy.id,
                    name=param_name,
                    value=str(param_value),
                    parameter_type=param_type
                )
                db.session.add(parameter)
            
            db.session.commit()
            
            logger.info(f"Created strategy {strategy.name} for user {current_user_id}")
            return {'strategy': strategy.to_dict()}, 201
            
        except Exception as e:
            logger.error(f"Error creating strategy: {str(e)}")
            db.session.rollback()
            return {'error': 'Failed to create strategy'}, 500
    
    @auth_required
    def put(self, current_user_id, strategy_id):
        """Update a strategy."""
        if not strategy_id:
            return {'error': 'Strategy ID is required'}, 400
        
        strategy = Strategy.query.filter_by(
            id=strategy_id, 
            user_id=current_user_id, 
            is_active=True
        ).first()
        
        if not strategy:
            return {'error': 'Strategy not found'}, 404
        
        data = request.get_json()
        if not data:
            return {'error': 'Request body is required'}, 400
        
        try:
            # Update basic fields
            if 'name' in data:
                strategy.name = data['name']
            if 'description' in data:
                strategy.description = data['description']
            if 'symbols' in data:
                strategy.symbols = data['symbols']
            if 'status' in data:
                try:
                    strategy.status = StrategyStatus(data['status'])
                except ValueError:
                    return {'error': 'Invalid status'}, 400
            
            # Update parameters if provided
            if 'parameters' in data:
                # Remove existing parameters
                StrategyParameter.query.filter_by(strategy_id=strategy.id).delete()
                
                # Add new parameters
                for param_name, param_value in data['parameters'].items():
                    if isinstance(param_value, bool):
                        param_type = 'bool'
                    elif isinstance(param_value, int):
                        param_type = 'int'
                    elif isinstance(param_value, float):
                        param_type = 'float'
                    else:
                        param_type = 'string'
                    
                    parameter = StrategyParameter(
                        strategy_id=strategy.id,
                        name=param_name,
                        value=str(param_value),
                        parameter_type=param_type
                    )
                    db.session.add(parameter)
            
            db.session.commit()
            
            logger.info(f"Updated strategy {strategy_id}")
            return {'strategy': strategy.to_dict()}, 200
            
        except Exception as e:
            logger.error(f"Error updating strategy: {str(e)}")
            db.session.rollback()
            return {'error': 'Failed to update strategy'}, 500
    
    @auth_required
    def delete(self, current_user_id, strategy_id):
        """Delete (deactivate) a strategy."""
        if not strategy_id:
            return {'error': 'Strategy ID is required'}, 400
        
        strategy = Strategy.query.filter_by(
            id=strategy_id, 
            user_id=current_user_id, 
            is_active=True
        ).first()
        
        if not strategy:
            return {'error': 'Strategy not found'}, 404
        
        try:
            strategy.is_active = False
            strategy.status = StrategyStatus.INACTIVE
            db.session.commit()
            
            logger.info(f"Deleted strategy {strategy_id}")
            return {'message': 'Strategy deleted successfully'}, 200
            
        except Exception as e:
            logger.error(f"Error deleting strategy: {str(e)}")
            db.session.rollback()
            return {'error': 'Failed to delete strategy'}, 500

class BacktestResource(Resource):
    """Strategy backtesting endpoints."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    @auth_required
    def post(self, current_user_id, strategy_id):
        """Run backtest for a strategy."""
        strategy = Strategy.query.filter_by(
            id=strategy_id, 
            user_id=current_user_id, 
            is_active=True
        ).first()
        
        if not strategy:
            return {'error': 'Strategy not found'}, 404
        
        data = request.get_json() or {}
        period = data.get('period', '1y')
        
        try:
            # Get strategy parameters
            parameters = {param.name: param.value for param in strategy.parameters}
            
            # Create strategy instance
            strategy_instance = StrategyFactory.create_strategy(
                strategy.strategy_type, 
                strategy.id, 
                parameters
            )
            
            if not strategy_instance:
                return {'error': 'Failed to create strategy instance'}, 500
            
            # Run backtest for each symbol
            backtest_results = {}
            
            for symbol in strategy.symbols:
                # Get historical data
                hist_data = self.market_data_service.get_historical_data(symbol, period)
                
                if hist_data is not None and not hist_data.empty:
                    # Generate signals
                    signals = strategy_instance.generate_signals(symbol, hist_data)
                    
                    # Simple backtest calculation
                    backtest_results[symbol] = self._calculate_backtest_metrics(
                        hist_data, signals, symbol
                    )
                else:
                    backtest_results[symbol] = {'error': 'No historical data available'}
            
            return {
                'strategy_id': strategy_id,
                'period': period,
                'results': backtest_results
            }, 200
            
        except Exception as e:
            logger.error(f"Error running backtest: {str(e)}")
            return {'error': 'Failed to run backtest'}, 500
    
    def _calculate_backtest_metrics(self, hist_data, signals, symbol):
        """Calculate basic backtest metrics."""
        try:
            if not signals:
                return {'error': 'No signals generated'}
            
            # Simple buy-and-hold vs strategy comparison
            initial_price = float(hist_data['Close'].iloc[0])
            final_price = float(hist_data['Close'].iloc[-1])
            buy_hold_return = (final_price - initial_price) / initial_price * 100
            
            # Calculate strategy returns (simplified)
            strategy_return = 0
            position = 0
            entry_price = 0
            
            for signal in signals:
                if signal['action'] == 'BUY' and position == 0:
                    position = 1
                    entry_price = signal['price']
                elif signal['action'] == 'SELL' and position == 1:
                    trade_return = (signal['price'] - entry_price) / entry_price * 100
                    strategy_return += trade_return
                    position = 0
            
            return {
                'symbol': symbol,
                'total_signals': len(signals),
                'buy_signals': len([s for s in signals if s['action'] == 'BUY']),
                'sell_signals': len([s for s in signals if s['action'] == 'SELL']),
                'strategy_return': round(strategy_return, 2),
                'buy_hold_return': round(buy_hold_return, 2),
                'outperformance': round(strategy_return - buy_hold_return, 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating backtest metrics: {str(e)}")
            return {'error': 'Failed to calculate metrics'}
