from flask import request, jsonify
from flask_restful import Resource
from app.services.market_data_service import MarketDataService
from app.utils.decorators import auth_required
import logging

logger = logging.getLogger(__name__)

class MarketDataResource(Resource):
    """Real-time market data endpoints."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    @auth_required
    def get(self, current_user_id, symbol):
        """Get real-time quote for a symbol."""
        if not symbol:
            return {'error': 'Symbol is required'}, 400
        
        # Check for cached data first
        cached_quote = self.market_data_service.get_cached_quote(symbol)
        if cached_quote:
            cached_quote['cached'] = True
            return {'quote': cached_quote}, 200
        
        # Fetch fresh data
        quote = self.market_data_service.get_real_time_quote(symbol)
        
        if quote:
            quote['cached'] = False
            return {'quote': quote}, 200
        else:
            return {'error': f'Unable to fetch data for symbol: {symbol}'}, 404

class HistoricalDataResource(Resource):
    """Historical market data endpoints."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    @auth_required
    def get(self, current_user_id, symbol):
        """Get historical data for a symbol."""
        if not symbol:
            return {'error': 'Symbol is required'}, 400
        
        # Get query parameters
        period = request.args.get('period', '1y')
        interval = request.args.get('interval', '1d')
        
        # Validate parameters
        valid_periods = ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max']
        valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
        
        if period not in valid_periods:
            return {'error': f'Invalid period. Valid options: {valid_periods}'}, 400
        
        if interval not in valid_intervals:
            return {'error': f'Invalid interval. Valid options: {valid_intervals}'}, 400
        
        # Fetch historical data
        hist_data = self.market_data_service.get_historical_data(symbol, period, interval)
        
        if hist_data is not None and not hist_data.empty:
            # Convert DataFrame to list of dictionaries
            data_list = []
            for date, row in hist_data.iterrows():
                data_list.append({
                    'date': date.isoformat(),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume']) if row['Volume'] else 0
                })
            
            return {
                'symbol': symbol.upper(),
                'period': period,
                'interval': interval,
                'data': data_list
            }, 200
        else:
            return {'error': f'Unable to fetch historical data for symbol: {symbol}'}, 404

class MultipleQuotesResource(Resource):
    """Multiple symbols quote endpoint."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    @auth_required
    def post(self, current_user_id):
        """Get quotes for multiple symbols."""
        data = request.get_json()
        
        if not data or 'symbols' not in data:
            return {'error': 'symbols list is required'}, 400
        
        symbols = data['symbols']
        
        if not isinstance(symbols, list) or len(symbols) == 0:
            return {'error': 'symbols must be a non-empty list'}, 400
        
        if len(symbols) > 50:  # Limit to prevent abuse
            return {'error': 'Maximum 50 symbols allowed per request'}, 400
        
        quotes = self.market_data_service.get_multiple_quotes(symbols)
        
        return {'quotes': quotes}, 200
