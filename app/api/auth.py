from flask import request, jsonify
from flask_restful import Resource
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from marshmallow import Schema, fields, ValidationError
from app.models.user import User
from app import db
from app.utils.decorators import handle_errors, validate_json
import logging

logger = logging.getLogger(__name__)

class LoginSchema(Schema):
    username = fields.Str(required=True)
    password = fields.Str(required=True)

class RegisterSchema(Schema):
    username = fields.Str(required=True)
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=lambda x: len(x) >= 6)

class LoginResource(Resource):
    """User login endpoint."""
    
    @handle_errors
    @validate_json(LoginSchema)
    def post(self, validated_data):
        """Authenticate user and return JWT token."""
        username = validated_data['username']
        password = validated_data['password']
        
        user = User.query.filter_by(username=username, is_active=True).first()
        
        if user and user.check_password(password):
            access_token = create_access_token(identity=user.id)
            
            logger.info(f"User {username} logged in successfully")
            
            return {
                'access_token': access_token,
                'user': user.to_dict()
            }, 200
        else:
            logger.warning(f"Failed login attempt for username: {username}")
            return {'error': 'Invalid credentials'}, 401

class AuthResource(Resource):
    """User registration and profile management."""
    
    @handle_errors
    @validate_json(RegisterSchema)
    def post(self, validated_data):
        """Register a new user."""
        username = validated_data['username']
        email = validated_data['email']
        password = validated_data['password']
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            return {'error': 'Username already exists'}, 400
        
        if User.query.filter_by(email=email).first():
            return {'error': 'Email already exists'}, 400
        
        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        # Create access token
        access_token = create_access_token(identity=user.id)
        
        logger.info(f"New user registered: {username}")
        
        return {
            'access_token': access_token,
            'user': user.to_dict()
        }, 201
    
    @jwt_required()
    @handle_errors
    def get(self):
        """Get current user profile."""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return {'error': 'User not found'}, 404
        
        return {'user': user.to_dict()}, 200
