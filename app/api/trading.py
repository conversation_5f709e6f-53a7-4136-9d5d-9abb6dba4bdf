from flask import request, jsonify
from flask_restful import Resource
from marshmallow import Schema, fields, ValidationError, validate
from app.services.order_service import OrderService
from app.utils.decorators import auth_required, validate_json
import logging

logger = logging.getLogger(__name__)

class CreateOrderSchema(Schema):
    portfolio_id = fields.Int(required=True)
    symbol = fields.Str(required=True)
    side = fields.Str(required=True, validate=validate.OneOf(['buy', 'sell']))
    order_type = fields.Str(required=True, validate=validate.OneOf(['market', 'limit', 'stop', 'stop_limit']))
    quantity = fields.Float(required=True, validate=lambda x: x > 0)
    price = fields.Float(allow_none=True, validate=lambda x: x is None or x > 0)
    stop_price = fields.Float(allow_none=True, validate=lambda x: x is None or x > 0)
    strategy_id = fields.Int(allow_none=True)

class OrderResource(Resource):
    """Order management endpoints."""
    
    def __init__(self):
        self.order_service = OrderService()
    
    @auth_required
    def get(self, current_user_id, order_id=None):
        """Get orders for the current user."""
        if order_id:
            # Get specific order
            orders = self.order_service.get_user_orders(current_user_id)
            order = next((o for o in orders if o.id == order_id), None)
            
            if not order:
                return {'error': 'Order not found'}, 404
            
            return {'order': order.to_dict()}, 200
        else:
            # Get all orders with optional filtering
            status = request.args.get('status')
            portfolio_id = request.args.get('portfolio_id', type=int)
            
            if portfolio_id:
                orders = self.order_service.get_portfolio_orders(portfolio_id, current_user_id)
            else:
                orders = self.order_service.get_user_orders(current_user_id, status)
            
            return {
                'orders': [order.to_dict() for order in orders]
            }, 200
    
    @auth_required
    @validate_json(CreateOrderSchema)
    def post(self, validated_data, current_user_id):
        """Create a new order."""
        order = self.order_service.create_order(
            user_id=current_user_id,
            portfolio_id=validated_data['portfolio_id'],
            symbol=validated_data['symbol'],
            side=validated_data['side'],
            order_type=validated_data['order_type'],
            quantity=validated_data['quantity'],
            price=validated_data.get('price'),
            stop_price=validated_data.get('stop_price'),
            strategy_id=validated_data.get('strategy_id')
        )
        
        if order:
            return {'order': order.to_dict()}, 201
        else:
            return {'error': 'Failed to create order'}, 500
    
    @auth_required
    def delete(self, current_user_id, order_id):
        """Cancel an order."""
        if not order_id:
            return {'error': 'Order ID is required'}, 400
        
        success = self.order_service.cancel_order(order_id, current_user_id)
        
        if success:
            return {'message': 'Order cancelled successfully'}, 200
        else:
            return {'error': 'Failed to cancel order'}, 500

class TradeResource(Resource):
    """Trade history endpoints."""
    
    def __init__(self):
        self.order_service = OrderService()
    
    @auth_required
    def get(self, current_user_id, trade_id=None):
        """Get trade history for the current user."""
        if trade_id:
            # Get specific trade
            trades = self.order_service.get_user_trades(current_user_id)
            trade = next((t for t in trades if t.id == trade_id), None)
            
            if not trade:
                return {'error': 'Trade not found'}, 404
            
            return {'trade': trade.to_dict()}, 200
        else:
            # Get all trades with optional filtering
            symbol = request.args.get('symbol')
            trades = self.order_service.get_user_trades(current_user_id, symbol)
            
            return {
                'trades': [trade.to_dict() for trade in trades]
            }, 200
