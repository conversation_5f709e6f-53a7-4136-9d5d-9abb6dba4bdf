from flask import request, jsonify
from flask_restful import Resource
from marshmallow import Schema, fields, ValidationError
from app.services.portfolio_service import PortfolioService
from app.utils.decorators import auth_required, validate_json
import logging

logger = logging.getLogger(__name__)

class CreatePortfolioSchema(Schema):
    name = fields.Str(required=True)
    initial_value = fields.Float(required=True, validate=lambda x: x > 0)

class PortfolioResource(Resource):
    """Portfolio management endpoints."""
    
    def __init__(self):
        self.portfolio_service = PortfolioService()
    
    @auth_required
    def get(self, current_user_id):
        """Get all portfolios for the current user."""
        portfolios = self.portfolio_service.get_user_portfolios(current_user_id)
        
        portfolio_data = []
        for portfolio in portfolios:
            # Get updated metrics
            metrics = self.portfolio_service.calculate_portfolio_metrics(portfolio.id)
            portfolio_dict = portfolio.to_dict()
            portfolio_dict.update(metrics)
            portfolio_data.append(portfolio_dict)
        
        return {'portfolios': portfolio_data}, 200
    
    @auth_required
    @validate_json(CreatePortfolioSchema)
    def post(self, validated_data, current_user_id):
        """Create a new portfolio."""
        name = validated_data['name']
        initial_value = validated_data['initial_value']
        
        portfolio = self.portfolio_service.create_portfolio(
            current_user_id, name, initial_value
        )
        
        if portfolio:
            return {'portfolio': portfolio.to_dict()}, 201
        else:
            return {'error': 'Failed to create portfolio'}, 500

class PositionsResource(Resource):
    """Portfolio positions endpoints."""
    
    def __init__(self):
        self.portfolio_service = PortfolioService()
    
    @auth_required
    def get(self, current_user_id):
        """Get positions for a specific portfolio."""
        portfolio_id = request.args.get('portfolio_id', type=int)
        
        if not portfolio_id:
            return {'error': 'portfolio_id parameter is required'}, 400
        
        # Verify portfolio ownership
        portfolio = self.portfolio_service.get_portfolio(portfolio_id, current_user_id)
        if not portfolio:
            return {'error': 'Portfolio not found'}, 404
        
        # Get portfolio metrics including positions
        metrics = self.portfolio_service.calculate_portfolio_metrics(portfolio_id)
        
        return {
            'portfolio_id': portfolio_id,
            'positions': metrics.get('positions', []),
            'summary': {
                'total_value': metrics.get('total_value', 0),
                'cash_balance': metrics.get('cash_balance', 0),
                'total_invested': metrics.get('total_invested', 0),
                'total_unrealized_pnl': metrics.get('total_unrealized_pnl', 0),
                'updated_at': metrics.get('updated_at')
            }
        }, 200
