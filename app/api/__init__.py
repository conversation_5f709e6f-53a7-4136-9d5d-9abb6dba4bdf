from flask import Blueprint
from flask_restful import Api

# Create blueprint
api_bp = Blueprint('api', __name__)
api = Api(api_bp)

# Import and register resources
from app.api.auth import AuthResource, LoginResource
from app.api.portfolio import PortfolioResource, PositionsResource
from app.api.trading import TradeResource, OrderResource
from app.api.market_data import MarketDataResource, HistoricalDataResource, MultipleQuotesResource
from app.api.strategies import StrategyResource, BacktestResource

# Authentication endpoints
api.add_resource(LoginResource, '/auth/login')
api.add_resource(AuthResource, '/auth/register')

# Portfolio endpoints
api.add_resource(PortfolioResource, '/portfolio')
api.add_resource(PositionsResource, '/portfolio/positions')

# Trading endpoints
api.add_resource(TradeResource, '/trades', '/trades/<int:trade_id>')
api.add_resource(OrderResource, '/orders', '/orders/<int:order_id>')

# Market data endpoints
api.add_resource(MarketDataResource, '/market-data/<string:symbol>')
api.add_resource(HistoricalDataResource, '/market-data/<string:symbol>/history')
api.add_resource(MultipleQuotesResource, '/market-data/quotes')

# Strategy endpoints
api.add_resource(StrategyResource, '/strategies', '/strategies/<int:strategy_id>')
api.add_resource(BacktestResource, '/strategies/<int:strategy_id>/backtest')
