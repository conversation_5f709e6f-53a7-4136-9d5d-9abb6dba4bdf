from datetime import datetime
from app import db

class MarketData(db.Model):
    """Real-time market data model."""
    __tablename__ = 'market_data'
    
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(10), nullable=False, index=True)
    price = db.Column(db.Numeric(10, 4), nullable=False)
    bid = db.Column(db.Numeric(10, 4), nullable=True)
    ask = db.Column(db.Numeric(10, 4), nullable=True)
    volume = db.Column(db.BigInteger, nullable=True)
    change = db.Column(db.Numeric(10, 4), nullable=True)
    change_percent = db.Column(db.Numeric(6, 4), nullable=True)
    market_cap = db.Column(db.BigInteger, nullable=True)
    pe_ratio = db.Column(db.Numeric(8, 2), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def to_dict(self):
        """Convert market data to dictionary."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'price': float(self.price),
            'bid': float(self.bid) if self.bid else None,
            'ask': float(self.ask) if self.ask else None,
            'volume': self.volume,
            'change': float(self.change) if self.change else None,
            'change_percent': float(self.change_percent) if self.change_percent else None,
            'market_cap': self.market_cap,
            'pe_ratio': float(self.pe_ratio) if self.pe_ratio else None,
            'timestamp': self.timestamp.isoformat()
        }

class HistoricalPrice(db.Model):
    """Historical price data model."""
    __tablename__ = 'historical_prices'
    
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(10), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    open_price = db.Column(db.Numeric(10, 4), nullable=False)
    high_price = db.Column(db.Numeric(10, 4), nullable=False)
    low_price = db.Column(db.Numeric(10, 4), nullable=False)
    close_price = db.Column(db.Numeric(10, 4), nullable=False)
    adjusted_close = db.Column(db.Numeric(10, 4), nullable=True)
    volume = db.Column(db.BigInteger, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Composite index for efficient queries
    __table_args__ = (
        db.Index('idx_symbol_date', 'symbol', 'date'),
    )
    
    def to_dict(self):
        """Convert historical price to dictionary."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'date': self.date.isoformat(),
            'open': float(self.open_price),
            'high': float(self.high_price),
            'low': float(self.low_price),
            'close': float(self.close_price),
            'adjusted_close': float(self.adjusted_close) if self.adjusted_close else None,
            'volume': self.volume,
            'created_at': self.created_at.isoformat()
        }
