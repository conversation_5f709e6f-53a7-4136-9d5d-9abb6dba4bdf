from datetime import datetime
from enum import Enum
from app import db

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class Order(db.Model):
    """Order model to track buy/sell orders."""
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    portfolio_id = db.Column(db.Integer, db.ForeignKey('portfolios.id'), nullable=False)
    symbol = db.Column(db.String(10), nullable=False, index=True)
    side = db.Column(db.Enum(OrderSide), nullable=False)
    order_type = db.Column(db.Enum(OrderType), nullable=False)
    quantity = db.Column(db.Numeric(15, 4), nullable=False)
    price = db.Column(db.Numeric(10, 4), nullable=True)  # For limit orders
    stop_price = db.Column(db.Numeric(10, 4), nullable=True)  # For stop orders
    filled_quantity = db.Column(db.Numeric(15, 4), default=0)
    average_fill_price = db.Column(db.Numeric(10, 4), nullable=True)
    status = db.Column(db.Enum(OrderStatus), default=OrderStatus.PENDING)
    strategy_id = db.Column(db.Integer, db.ForeignKey('strategies.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    filled_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    trades = db.relationship('Trade', backref='order', lazy=True)
    
    @property
    def remaining_quantity(self):
        """Calculate remaining quantity to be filled."""
        return float(self.quantity) - float(self.filled_quantity)
    
    def to_dict(self):
        """Convert order to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'portfolio_id': self.portfolio_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': float(self.quantity),
            'price': float(self.price) if self.price else None,
            'stop_price': float(self.stop_price) if self.stop_price else None,
            'filled_quantity': float(self.filled_quantity),
            'remaining_quantity': self.remaining_quantity,
            'average_fill_price': float(self.average_fill_price) if self.average_fill_price else None,
            'status': self.status.value,
            'strategy_id': self.strategy_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'filled_at': self.filled_at.isoformat() if self.filled_at else None
        }

class Trade(db.Model):
    """Trade model to track executed trades."""
    __tablename__ = 'trades'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    portfolio_id = db.Column(db.Integer, db.ForeignKey('portfolios.id'), nullable=False)
    symbol = db.Column(db.String(10), nullable=False, index=True)
    side = db.Column(db.Enum(OrderSide), nullable=False)
    quantity = db.Column(db.Numeric(15, 4), nullable=False)
    price = db.Column(db.Numeric(10, 4), nullable=False)
    commission = db.Column(db.Numeric(10, 4), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    executed_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert trade to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'order_id': self.order_id,
            'portfolio_id': self.portfolio_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': float(self.quantity),
            'price': float(self.price),
            'commission': float(self.commission),
            'total_amount': float(self.total_amount),
            'executed_at': self.executed_at.isoformat()
        }
