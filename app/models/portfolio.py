from datetime import datetime
from app import db

class Portfolio(db.Model):
    """Portfolio model to track user's overall portfolio."""
    __tablename__ = 'portfolios'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    initial_value = db.Column(db.Numeric(15, 2), nullable=False)
    current_value = db.Column(db.Numeric(15, 2), nullable=False)
    cash_balance = db.Column(db.Numeric(15, 2), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    positions = db.relationship('Position', backref='portfolio', lazy=True, cascade='all, delete-orphan')
    
    def calculate_total_value(self):
        """Calculate total portfolio value including positions."""
        positions_value = sum(pos.current_value for pos in self.positions if pos.is_active)
        return float(self.cash_balance) + positions_value
    
    def to_dict(self):
        """Convert portfolio to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'initial_value': float(self.initial_value),
            'current_value': float(self.current_value),
            'cash_balance': float(self.cash_balance),
            'total_value': self.calculate_total_value(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Position(db.Model):
    """Position model to track individual stock positions."""
    __tablename__ = 'positions'
    
    id = db.Column(db.Integer, primary_key=True)
    portfolio_id = db.Column(db.Integer, db.ForeignKey('portfolios.id'), nullable=False)
    symbol = db.Column(db.String(10), nullable=False, index=True)
    quantity = db.Column(db.Numeric(15, 4), nullable=False)
    average_cost = db.Column(db.Numeric(10, 4), nullable=False)
    current_price = db.Column(db.Numeric(10, 4), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    opened_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @property
    def current_value(self):
        """Calculate current value of position."""
        if self.current_price and self.quantity:
            return float(self.quantity) * float(self.current_price)
        return float(self.quantity) * float(self.average_cost)
    
    @property
    def unrealized_pnl(self):
        """Calculate unrealized P&L."""
        if self.current_price:
            return (float(self.current_price) - float(self.average_cost)) * float(self.quantity)
        return 0.0
    
    @property
    def unrealized_pnl_percent(self):
        """Calculate unrealized P&L percentage."""
        if self.average_cost and self.current_price:
            return ((float(self.current_price) - float(self.average_cost)) / float(self.average_cost)) * 100
        return 0.0
    
    def to_dict(self):
        """Convert position to dictionary."""
        return {
            'id': self.id,
            'portfolio_id': self.portfolio_id,
            'symbol': self.symbol,
            'quantity': float(self.quantity),
            'average_cost': float(self.average_cost),
            'current_price': float(self.current_price) if self.current_price else None,
            'current_value': self.current_value,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_percent': self.unrealized_pnl_percent,
            'is_active': self.is_active,
            'opened_at': self.opened_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
