from datetime import datetime
from enum import Enum
from app import db

class StrategyStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    BACKTESTING = "backtesting"

class Strategy(db.Model):
    """Trading strategy model."""
    __tablename__ = 'strategies'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    strategy_type = db.Column(db.String(50), nullable=False)  # e.g., 'moving_average', 'rsi', 'macd'
    symbols = db.Column(db.JSON, nullable=False)  # List of symbols to trade
    status = db.Column(db.Enum(StrategyStatus), default=StrategyStatus.INACTIVE)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Performance metrics
    total_return = db.Column(db.Numeric(10, 4), default=0)
    win_rate = db.Column(db.Numeric(5, 4), default=0)
    sharpe_ratio = db.Column(db.Numeric(8, 4), default=0)
    max_drawdown = db.Column(db.Numeric(8, 4), default=0)
    
    # Relationships
    parameters = db.relationship('StrategyParameter', backref='strategy', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='strategy', lazy=True)
    
    def to_dict(self):
        """Convert strategy to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'strategy_type': self.strategy_type,
            'symbols': self.symbols,
            'status': self.status.value,
            'is_active': self.is_active,
            'total_return': float(self.total_return),
            'win_rate': float(self.win_rate),
            'sharpe_ratio': float(self.sharpe_ratio),
            'max_drawdown': float(self.max_drawdown),
            'parameters': [param.to_dict() for param in self.parameters],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class StrategyParameter(db.Model):
    """Strategy parameter model for configurable strategy parameters."""
    __tablename__ = 'strategy_parameters'
    
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('strategies.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    value = db.Column(db.String(100), nullable=False)
    parameter_type = db.Column(db.String(20), nullable=False)  # 'int', 'float', 'string', 'bool'
    description = db.Column(db.Text, nullable=True)
    
    def to_dict(self):
        """Convert strategy parameter to dictionary."""
        # Convert value based on type
        if self.parameter_type == 'int':
            value = int(self.value)
        elif self.parameter_type == 'float':
            value = float(self.value)
        elif self.parameter_type == 'bool':
            value = self.value.lower() == 'true'
        else:
            value = self.value
            
        return {
            'id': self.id,
            'strategy_id': self.strategy_id,
            'name': self.name,
            'value': value,
            'parameter_type': self.parameter_type,
            'description': self.description
        }
