from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime
from app import db
from app.models.portfolio import Portfolio, Position
from app.models.trade import Trade, Order, OrderSide
from app.services.market_data_service import MarketDataService
import logging

logger = logging.getLogger(__name__)

class PortfolioService:
    """Service for managing portfolios and positions."""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
    
    def create_portfolio(self, user_id: int, name: str, initial_value: float) -> Optional[Portfolio]:
        """Create a new portfolio for a user."""
        try:
            portfolio = Portfolio(
                user_id=user_id,
                name=name,
                initial_value=Decimal(str(initial_value)),
                current_value=Decimal(str(initial_value)),
                cash_balance=Decimal(str(initial_value))
            )
            
            db.session.add(portfolio)
            db.session.commit()
            
            logger.info(f"Created portfolio {name} for user {user_id}")
            return portfolio
            
        except Exception as e:
            logger.error(f"Error creating portfolio: {str(e)}")
            db.session.rollback()
            return None
    
    def get_user_portfolios(self, user_id: int) -> List[Portfolio]:
        """Get all portfolios for a user."""
        return Portfolio.query.filter_by(user_id=user_id, is_active=True).all()
    
    def get_portfolio(self, portfolio_id: int, user_id: int) -> Optional[Portfolio]:
        """Get a specific portfolio for a user."""
        return Portfolio.query.filter_by(
            id=portfolio_id, 
            user_id=user_id, 
            is_active=True
        ).first()
    
    def update_portfolio_value(self, portfolio_id: int) -> Optional[Portfolio]:
        """Update portfolio current value based on positions."""
        try:
            portfolio = Portfolio.query.get(portfolio_id)
            if not portfolio:
                return None
            
            # Update position prices
            self._update_position_prices(portfolio_id)
            
            # Calculate total value
            total_value = self._calculate_total_portfolio_value(portfolio)
            portfolio.current_value = Decimal(str(total_value))
            portfolio.updated_at = datetime.utcnow()
            
            db.session.commit()
            return portfolio
            
        except Exception as e:
            logger.error(f"Error updating portfolio value: {str(e)}")
            db.session.rollback()
            return None
    
    def get_portfolio_positions(self, portfolio_id: int) -> List[Position]:
        """Get all active positions for a portfolio."""
        return Position.query.filter_by(
            portfolio_id=portfolio_id, 
            is_active=True
        ).all()
    
    def create_or_update_position(self, portfolio_id: int, symbol: str, 
                                quantity: float, price: float, side: OrderSide) -> Optional[Position]:
        """Create new position or update existing one."""
        try:
            existing_position = Position.query.filter_by(
                portfolio_id=portfolio_id,
                symbol=symbol.upper(),
                is_active=True
            ).first()
            
            if existing_position:
                # Update existing position
                if side == OrderSide.BUY:
                    # Add to position
                    total_cost = (float(existing_position.quantity) * float(existing_position.average_cost) + 
                                quantity * price)
                    new_quantity = float(existing_position.quantity) + quantity
                    new_avg_cost = total_cost / new_quantity
                    
                    existing_position.quantity = Decimal(str(new_quantity))
                    existing_position.average_cost = Decimal(str(new_avg_cost))
                else:
                    # Reduce position
                    new_quantity = float(existing_position.quantity) - quantity
                    if new_quantity <= 0:
                        existing_position.is_active = False
                        existing_position.quantity = Decimal('0')
                    else:
                        existing_position.quantity = Decimal(str(new_quantity))
                
                existing_position.updated_at = datetime.utcnow()
                position = existing_position
            else:
                # Create new position (only for BUY orders)
                if side == OrderSide.BUY:
                    position = Position(
                        portfolio_id=portfolio_id,
                        symbol=symbol.upper(),
                        quantity=Decimal(str(quantity)),
                        average_cost=Decimal(str(price))
                    )
                    db.session.add(position)
                else:
                    logger.warning(f"Cannot create position with SELL order for {symbol}")
                    return None
            
            db.session.commit()
            return position
            
        except Exception as e:
            logger.error(f"Error creating/updating position: {str(e)}")
            db.session.rollback()
            return None
    
    def calculate_portfolio_metrics(self, portfolio_id: int) -> Dict:
        """Calculate portfolio performance metrics."""
        try:
            portfolio = Portfolio.query.get(portfolio_id)
            if not portfolio:
                return {}
            
            positions = self.get_portfolio_positions(portfolio_id)
            
            # Update current values
            self._update_position_prices(portfolio_id)
            
            total_value = float(portfolio.cash_balance)
            total_unrealized_pnl = 0
            total_invested = 0
            
            position_details = []
            
            for position in positions:
                if position.current_price:
                    current_value = float(position.quantity) * float(position.current_price)
                    invested_value = float(position.quantity) * float(position.average_cost)
                    unrealized_pnl = current_value - invested_value
                    
                    total_value += current_value
                    total_unrealized_pnl += unrealized_pnl
                    total_invested += invested_value
                    
                    position_details.append({
                        'symbol': position.symbol,
                        'quantity': float(position.quantity),
                        'average_cost': float(position.average_cost),
                        'current_price': float(position.current_price),
                        'current_value': current_value,
                        'unrealized_pnl': unrealized_pnl,
                        'unrealized_pnl_percent': (unrealized_pnl / invested_value * 100) if invested_value > 0 else 0
                    })
            
            # Calculate overall metrics
            total_return = total_value - float(portfolio.initial_value)
            total_return_percent = (total_return / float(portfolio.initial_value) * 100) if portfolio.initial_value > 0 else 0
            
            return {
                'portfolio_id': portfolio_id,
                'total_value': total_value,
                'cash_balance': float(portfolio.cash_balance),
                'initial_value': float(portfolio.initial_value),
                'total_return': total_return,
                'total_return_percent': total_return_percent,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_invested': total_invested,
                'positions': position_details,
                'updated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {str(e)}")
            return {}
    
    def _update_position_prices(self, portfolio_id: int):
        """Update current prices for all positions in portfolio."""
        try:
            positions = Position.query.filter_by(
                portfolio_id=portfolio_id, 
                is_active=True
            ).all()
            
            symbols = [pos.symbol for pos in positions]
            if not symbols:
                return
            
            # Get current prices
            quotes = self.market_data_service.get_multiple_quotes(symbols)
            
            for position in positions:
                if position.symbol in quotes:
                    quote = quotes[position.symbol]
                    if quote and quote.get('price'):
                        position.current_price = Decimal(str(quote['price']))
                        position.updated_at = datetime.utcnow()
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Error updating position prices: {str(e)}")
            db.session.rollback()
    
    def _calculate_total_portfolio_value(self, portfolio: Portfolio) -> float:
        """Calculate total portfolio value including cash and positions."""
        total_value = float(portfolio.cash_balance)
        
        for position in portfolio.positions:
            if position.is_active and position.current_price:
                position_value = float(position.quantity) * float(position.current_price)
                total_value += position_value
        
        return total_value
    
    def update_cash_balance(self, portfolio_id: int, amount: float, operation: str = 'subtract'):
        """Update portfolio cash balance."""
        try:
            portfolio = Portfolio.query.get(portfolio_id)
            if not portfolio:
                return False
            
            if operation == 'add':
                portfolio.cash_balance += Decimal(str(amount))
            else:  # subtract
                if float(portfolio.cash_balance) >= amount:
                    portfolio.cash_balance -= Decimal(str(amount))
                else:
                    logger.warning(f"Insufficient cash balance in portfolio {portfolio_id}")
                    return False
            
            portfolio.updated_at = datetime.utcnow()
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error updating cash balance: {str(e)}")
            db.session.rollback()
            return False
