from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime
from app import db
from app.models.trade import Order, Trade, OrderType, OrderSide, OrderStatus
from app.models.portfolio import Portfolio
from app.services.portfolio_service import PortfolioService
from app.services.market_data_service import MarketDataService
import logging

logger = logging.getLogger(__name__)

class OrderService:
    """Service for managing orders and trade execution."""
    
    def __init__(self):
        self.portfolio_service = PortfolioService()
        self.market_data_service = MarketDataService()
    
    def create_order(self, user_id: int, portfolio_id: int, symbol: str, 
                    side: str, order_type: str, quantity: float, 
                    price: Optional[float] = None, stop_price: Optional[float] = None,
                    strategy_id: Optional[int] = None) -> Optional[Order]:
        """Create a new order."""
        try:
            # Validate portfolio ownership
            portfolio = self.portfolio_service.get_portfolio(portfolio_id, user_id)
            if not portfolio:
                logger.warning(f"Portfolio {portfolio_id} not found for user {user_id}")
                return None
            
            # Convert string enums
            order_side = OrderSide(side.lower())
            order_type_enum = OrderType(order_type.lower())
            
            # Validate order parameters
            if not self._validate_order(portfolio, symbol, order_side, quantity, price):
                return None
            
            # Create order
            order = Order(
                user_id=user_id,
                portfolio_id=portfolio_id,
                symbol=symbol.upper(),
                side=order_side,
                order_type=order_type_enum,
                quantity=Decimal(str(quantity)),
                price=Decimal(str(price)) if price else None,
                stop_price=Decimal(str(stop_price)) if stop_price else None,
                strategy_id=strategy_id
            )
            
            db.session.add(order)
            db.session.commit()
            
            # Try to execute immediately for market orders
            if order_type_enum == OrderType.MARKET:
                self._execute_market_order(order)
            
            logger.info(f"Created order {order.id} for {symbol}")
            return order
            
        except Exception as e:
            logger.error(f"Error creating order: {str(e)}")
            db.session.rollback()
            return None
    
    def cancel_order(self, order_id: int, user_id: int) -> bool:
        """Cancel an existing order."""
        try:
            order = Order.query.filter_by(id=order_id, user_id=user_id).first()
            if not order:
                logger.warning(f"Order {order_id} not found for user {user_id}")
                return False
            
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                logger.warning(f"Cannot cancel order {order_id} with status {order.status}")
                return False
            
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.utcnow()
            
            db.session.commit()
            logger.info(f"Cancelled order {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling order: {str(e)}")
            db.session.rollback()
            return False
    
    def get_user_orders(self, user_id: int, status: Optional[str] = None) -> List[Order]:
        """Get orders for a user, optionally filtered by status."""
        query = Order.query.filter_by(user_id=user_id)
        
        if status:
            try:
                status_enum = OrderStatus(status.lower())
                query = query.filter_by(status=status_enum)
            except ValueError:
                logger.warning(f"Invalid order status: {status}")
                return []
        
        return query.order_by(Order.created_at.desc()).all()
    
    def get_portfolio_orders(self, portfolio_id: int, user_id: int) -> List[Order]:
        """Get orders for a specific portfolio."""
        return Order.query.filter_by(
            portfolio_id=portfolio_id, 
            user_id=user_id
        ).order_by(Order.created_at.desc()).all()
    
    def execute_order(self, order_id: int, execution_price: float, 
                     execution_quantity: Optional[float] = None) -> Optional[Trade]:
        """Execute an order (full or partial)."""
        try:
            order = Order.query.get(order_id)
            if not order:
                logger.warning(f"Order {order_id} not found")
                return None
            
            if order.status not in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]:
                logger.warning(f"Cannot execute order {order_id} with status {order.status}")
                return None
            
            # Determine execution quantity
            if execution_quantity is None:
                execution_quantity = float(order.remaining_quantity)
            else:
                execution_quantity = min(execution_quantity, float(order.remaining_quantity))
            
            # Calculate total amount
            total_amount = execution_quantity * execution_price
            commission = self._calculate_commission(total_amount)
            
            # Create trade record
            trade = Trade(
                user_id=order.user_id,
                order_id=order.id,
                portfolio_id=order.portfolio_id,
                symbol=order.symbol,
                side=order.side,
                quantity=Decimal(str(execution_quantity)),
                price=Decimal(str(execution_price)),
                commission=Decimal(str(commission)),
                total_amount=Decimal(str(total_amount + commission))
            )
            
            db.session.add(trade)
            
            # Update order
            order.filled_quantity += Decimal(str(execution_quantity))
            
            if order.filled_quantity >= order.quantity:
                order.status = OrderStatus.FILLED
                order.filled_at = datetime.utcnow()
            else:
                order.status = OrderStatus.PARTIALLY_FILLED
            
            # Calculate average fill price
            total_filled_value = float(order.filled_quantity) * execution_price
            if order.average_fill_price:
                # Weighted average of previous fills and current fill
                prev_value = (float(order.filled_quantity) - execution_quantity) * float(order.average_fill_price)
                total_filled_value = prev_value + (execution_quantity * execution_price)
            
            order.average_fill_price = Decimal(str(total_filled_value / float(order.filled_quantity)))
            order.updated_at = datetime.utcnow()
            
            # Update portfolio
            self._update_portfolio_after_trade(trade)
            
            db.session.commit()
            
            logger.info(f"Executed order {order_id}: {execution_quantity} shares at ${execution_price}")
            return trade
            
        except Exception as e:
            logger.error(f"Error executing order: {str(e)}")
            db.session.rollback()
            return None
    
    def get_user_trades(self, user_id: int, symbol: Optional[str] = None) -> List[Trade]:
        """Get trade history for a user."""
        query = Trade.query.filter_by(user_id=user_id)
        
        if symbol:
            query = query.filter_by(symbol=symbol.upper())
        
        return query.order_by(Trade.executed_at.desc()).all()
    
    def _validate_order(self, portfolio: Portfolio, symbol: str, side: OrderSide, 
                       quantity: float, price: Optional[float]) -> bool:
        """Validate order parameters."""
        try:
            # Check quantity
            if quantity <= 0:
                logger.warning("Order quantity must be positive")
                return False
            
            # Check price for limit orders
            if price is not None and price <= 0:
                logger.warning("Order price must be positive")
                return False
            
            # For buy orders, check if enough cash
            if side == OrderSide.BUY:
                estimated_cost = quantity * (price or self._get_current_price(symbol))
                if estimated_cost > float(portfolio.cash_balance):
                    logger.warning(f"Insufficient cash balance for order")
                    return False
            
            # For sell orders, check if enough shares
            elif side == OrderSide.SELL:
                position = self.portfolio_service.get_portfolio_positions(portfolio.id)
                symbol_position = next((p for p in position if p.symbol == symbol.upper()), None)
                
                if not symbol_position or float(symbol_position.quantity) < quantity:
                    logger.warning(f"Insufficient shares for sell order")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating order: {str(e)}")
            return False
    
    def _execute_market_order(self, order: Order):
        """Execute a market order immediately."""
        try:
            # Get current market price
            quote = self.market_data_service.get_real_time_quote(order.symbol)
            if not quote or not quote.get('price'):
                logger.warning(f"Cannot get market price for {order.symbol}")
                order.status = OrderStatus.REJECTED
                db.session.commit()
                return
            
            execution_price = float(quote['price'])
            self.execute_order(order.id, execution_price)
            
        except Exception as e:
            logger.error(f"Error executing market order: {str(e)}")
            order.status = OrderStatus.REJECTED
            db.session.commit()
    
    def _update_portfolio_after_trade(self, trade: Trade):
        """Update portfolio positions and cash after trade execution."""
        try:
            # Update cash balance
            if trade.side == OrderSide.BUY:
                # Subtract cash for buy orders
                self.portfolio_service.update_cash_balance(
                    trade.portfolio_id, 
                    float(trade.total_amount), 
                    'subtract'
                )
            else:
                # Add cash for sell orders
                self.portfolio_service.update_cash_balance(
                    trade.portfolio_id, 
                    float(trade.total_amount), 
                    'add'
                )
            
            # Update position
            self.portfolio_service.create_or_update_position(
                trade.portfolio_id,
                trade.symbol,
                float(trade.quantity),
                float(trade.price),
                trade.side
            )
            
        except Exception as e:
            logger.error(f"Error updating portfolio after trade: {str(e)}")
    
    def _calculate_commission(self, trade_amount: float) -> float:
        """Calculate commission for a trade."""
        # Simple flat commission structure
        return min(trade_amount * 0.001, 10.0)  # 0.1% with $10 max
    
    def _get_current_price(self, symbol: str) -> float:
        """Get current market price for a symbol."""
        quote = self.market_data_service.get_real_time_quote(symbol)
        if quote and quote.get('price'):
            return float(quote['price'])
        return 0.0
