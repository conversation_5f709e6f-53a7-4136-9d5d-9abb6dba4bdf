import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from abc import ABC, abstractmethod
from app.services.market_data_service import MarketDataService
from app.models.strategy import Strategy, StrategyParameter
import logging

logger = logging.getLogger(__name__)

class BaseStrategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(self, strategy_id: int, parameters: Dict):
        self.strategy_id = strategy_id
        self.parameters = parameters
        self.market_data_service = MarketDataService()
    
    @abstractmethod
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[Dict]:
        """Generate trading signals based on the strategy logic."""
        pass
    
    @abstractmethod
    def get_required_parameters(self) -> List[Dict]:
        """Return list of required parameters for this strategy."""
        pass
    
    def calculate_position_size(self, portfolio_value: float, risk_per_trade: float = 0.02) -> float:
        """Calculate position size based on portfolio value and risk."""
        return portfolio_value * risk_per_trade

class MovingAverageStrategy(BaseStrategy):
    """Simple Moving Average Crossover Strategy."""
    
    def get_required_parameters(self) -> List[Dict]:
        return [
            {'name': 'short_window', 'type': 'int', 'default': 20, 'description': 'Short MA period'},
            {'name': 'long_window', 'type': 'int', 'default': 50, 'description': 'Long MA period'},
            {'name': 'risk_per_trade', 'type': 'float', 'default': 0.02, 'description': 'Risk per trade (%)'}
        ]
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[Dict]:
        """Generate signals based on moving average crossover."""
        signals = []
        
        try:
            short_window = int(self.parameters.get('short_window', 20))
            long_window = int(self.parameters.get('long_window', 50))
            
            # Calculate moving averages
            data['MA_short'] = data['Close'].rolling(window=short_window).mean()
            data['MA_long'] = data['Close'].rolling(window=long_window).mean()
            
            # Generate signals
            data['signal'] = 0
            data['signal'][short_window:] = np.where(
                data['MA_short'][short_window:] > data['MA_long'][short_window:], 1, 0
            )
            data['positions'] = data['signal'].diff()
            
            # Extract buy/sell signals
            for idx, row in data.iterrows():
                if row['positions'] == 1:  # Buy signal
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': row['Close'],
                        'timestamp': idx,
                        'confidence': self._calculate_confidence(row, data.loc[:idx])
                    })
                elif row['positions'] == -1:  # Sell signal
                    signals.append({
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': row['Close'],
                        'timestamp': idx,
                        'confidence': self._calculate_confidence(row, data.loc[:idx])
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating MA signals for {symbol}: {str(e)}")
            return []
    
    def _calculate_confidence(self, current_row: pd.Series, historical_data: pd.DataFrame) -> float:
        """Calculate confidence score for the signal."""
        try:
            # Simple confidence based on MA separation
            ma_diff = abs(current_row['MA_short'] - current_row['MA_long'])
            price = current_row['Close']
            confidence = min(ma_diff / price * 100, 1.0)  # Normalize to 0-1
            return round(confidence, 3)
        except:
            return 0.5

class RSIStrategy(BaseStrategy):
    """RSI-based trading strategy."""
    
    def get_required_parameters(self) -> List[Dict]:
        return [
            {'name': 'rsi_period', 'type': 'int', 'default': 14, 'description': 'RSI calculation period'},
            {'name': 'oversold_threshold', 'type': 'float', 'default': 30, 'description': 'RSI oversold level'},
            {'name': 'overbought_threshold', 'type': 'float', 'default': 70, 'description': 'RSI overbought level'},
            {'name': 'risk_per_trade', 'type': 'float', 'default': 0.02, 'description': 'Risk per trade (%)'}
        ]
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[Dict]:
        """Generate signals based on RSI levels."""
        signals = []
        
        try:
            rsi_period = int(self.parameters.get('rsi_period', 14))
            oversold = float(self.parameters.get('oversold_threshold', 30))
            overbought = float(self.parameters.get('overbought_threshold', 70))
            
            # Calculate RSI
            data['RSI'] = self._calculate_rsi(data['Close'], rsi_period)
            
            # Generate signals
            for i in range(1, len(data)):
                current_rsi = data['RSI'].iloc[i]
                prev_rsi = data['RSI'].iloc[i-1]
                
                # Buy signal: RSI crosses above oversold level
                if prev_rsi <= oversold and current_rsi > oversold:
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': data['Close'].iloc[i],
                        'timestamp': data.index[i],
                        'confidence': self._calculate_rsi_confidence(current_rsi, 'BUY')
                    })
                
                # Sell signal: RSI crosses below overbought level
                elif prev_rsi >= overbought and current_rsi < overbought:
                    signals.append({
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': data['Close'].iloc[i],
                        'timestamp': data.index[i],
                        'confidence': self._calculate_rsi_confidence(current_rsi, 'SELL')
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating RSI signals for {symbol}: {str(e)}")
            return []
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_rsi_confidence(self, rsi_value: float, action: str) -> float:
        """Calculate confidence based on RSI value."""
        if action == 'BUY':
            # More confident when RSI is lower (more oversold)
            confidence = max(0, (50 - rsi_value) / 50)
        else:  # SELL
            # More confident when RSI is higher (more overbought)
            confidence = max(0, (rsi_value - 50) / 50)
        
        return round(min(confidence, 1.0), 3)

class StrategyFactory:
    """Factory class to create strategy instances."""
    
    STRATEGIES = {
        'moving_average': MovingAverageStrategy,
        'rsi': RSIStrategy,
    }
    
    @classmethod
    def create_strategy(cls, strategy_type: str, strategy_id: int, parameters: Dict) -> Optional[BaseStrategy]:
        """Create a strategy instance."""
        strategy_class = cls.STRATEGIES.get(strategy_type)
        if strategy_class:
            return strategy_class(strategy_id, parameters)
        return None
    
    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """Get list of available strategy types."""
        return list(cls.STRATEGIES.keys())
    
    @classmethod
    def get_strategy_parameters(cls, strategy_type: str) -> List[Dict]:
        """Get required parameters for a strategy type."""
        strategy_class = cls.STRATEGIES.get(strategy_type)
        if strategy_class:
            # Create temporary instance to get parameters
            temp_instance = strategy_class(0, {})
            return temp_instance.get_required_parameters()
        return []
