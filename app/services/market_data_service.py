import yfinance as yf
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
from flask import current_app
from app.models.market_data import MarketData, HistoricalPrice
from app import db
import logging

logger = logging.getLogger(__name__)

class MarketDataService:
    """Service for fetching and managing market data from various providers."""
    
    def __init__(self):
        self.alpha_vantage_key = current_app.config.get('ALPHA_VANTAGE_API_KEY')
        self.polygon_key = current_app.config.get('POLYGON_API_KEY')
        self.iex_key = current_app.config.get('IEX_CLOUD_API_KEY')
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict]:
        """Get real-time quote for a symbol using Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                logger.warning(f"No data found for symbol: {symbol}")
                return None
            
            # Extract relevant data
            quote_data = {
                'symbol': symbol.upper(),
                'price': info.get('currentPrice') or info.get('regularMarketPrice'),
                'bid': info.get('bid'),
                'ask': info.get('ask'),
                'volume': info.get('volume') or info.get('regularMarketVolume'),
                'change': info.get('regularMarketChange'),
                'change_percent': info.get('regularMarketChangePercent'),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'timestamp': datetime.utcnow()
            }
            
            # Save to database
            self._save_market_data(quote_data)
            
            return quote_data
            
        except Exception as e:
            logger.error(f"Error fetching real-time quote for {symbol}: {str(e)}")
            return None
    
    def get_historical_data(self, symbol: str, period: str = "1y", interval: str = "1d") -> Optional[pd.DataFrame]:
        """Get historical data for a symbol."""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                logger.warning(f"No historical data found for symbol: {symbol}")
                return None
            
            # Save to database
            self._save_historical_data(symbol, hist)
            
            return hist
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {str(e)}")
            return None
    
    def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get real-time quotes for multiple symbols."""
        quotes = {}
        for symbol in symbols:
            quote = self.get_real_time_quote(symbol)
            if quote:
                quotes[symbol] = quote
        return quotes
    
    def _save_market_data(self, quote_data: Dict):
        """Save market data to database."""
        try:
            market_data = MarketData(
                symbol=quote_data['symbol'],
                price=quote_data['price'],
                bid=quote_data.get('bid'),
                ask=quote_data.get('ask'),
                volume=quote_data.get('volume'),
                change=quote_data.get('change'),
                change_percent=quote_data.get('change_percent'),
                market_cap=quote_data.get('market_cap'),
                pe_ratio=quote_data.get('pe_ratio'),
                timestamp=quote_data['timestamp']
            )
            
            db.session.add(market_data)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Error saving market data: {str(e)}")
            db.session.rollback()
    
    def _save_historical_data(self, symbol: str, hist_data: pd.DataFrame):
        """Save historical data to database."""
        try:
            for date, row in hist_data.iterrows():
                # Check if data already exists
                existing = HistoricalPrice.query.filter_by(
                    symbol=symbol.upper(),
                    date=date.date()
                ).first()
                
                if not existing:
                    historical_price = HistoricalPrice(
                        symbol=symbol.upper(),
                        date=date.date(),
                        open_price=row['Open'],
                        high_price=row['High'],
                        low_price=row['Low'],
                        close_price=row['Close'],
                        volume=row['Volume']
                    )
                    
                    db.session.add(historical_price)
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Error saving historical data: {str(e)}")
            db.session.rollback()
    
    def get_cached_quote(self, symbol: str, max_age_minutes: int = 5) -> Optional[Dict]:
        """Get cached quote from database if recent enough."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=max_age_minutes)
            
            market_data = MarketData.query.filter(
                MarketData.symbol == symbol.upper(),
                MarketData.timestamp >= cutoff_time
            ).order_by(MarketData.timestamp.desc()).first()
            
            if market_data:
                return market_data.to_dict()
            
            return None
            
        except Exception as e:
            logger.error(f"Error fetching cached quote: {str(e)}")
            return None
